import { User, Requirement, Bid, RiderApplication, Location } from '@/lib/types'
import { generateId } from '@/lib/utils'

// Mock Users
export const mockUsers: User[] = [
  // Buyers
  {
    id: 'buyer_swiggy',
    name: '<PERSON> Larry',
    email: '<EMAIL>',
    type: 'buyer',
    company: 'Swiggy',
    phone: '+91 9876543210',
    location: 'Bangalore',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'buyer_zomato',
    name: '<PERSON> Olivia',
    email: '<EMAIL>',
    type: 'buyer',
    company: 'Zomato',
    phone: '+91 9876543211',
    location: 'Bangalore',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'buyer_blinkit',
    name: 'Manager <PERSON>',
    email: '<EMAIL>',
    type: 'buyer',
    company: 'Blinkit',
    phone: '+91 9876543212',
    location: 'Bangalore',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  // Suppliers
  {
    id: 'supplier_yana',
    name: '<PERSON> Fiona',
    email: '<EMAIL>',
    type: 'supplier',
    company: 'Yana Fleet Services',
    phone: '+91 9876543220',
    location: 'Bangalore',
    reliabilityScore: 4.7,
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'supplier_rapidriders',
    name: 'Supplier Sam',
    email: '<EMAIL>',
    type: 'supplier',
    company: 'Rapid Riders',
    phone: '+91 9876543221',
    location: 'Bangalore',
    reliabilityScore: 4.3,
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  // Solo Riders
  {
    id: 'rider_raj',
    name: 'Rider Raj',
    email: '<EMAIL>',
    type: 'rider',
    phone: '+91 9876543230',
    location: 'Marathahalli, Bangalore',
    reliabilityScore: 4.5,
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'rider_kumar',
    name: 'Kumar K',
    email: '<EMAIL>',
    type: 'rider',
    phone: '+91 9876543231',
    location: 'Koramangala, Bangalore',
    reliabilityScore: 4.8,
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'rider_priya',
    name: 'Priya P',
    email: '<EMAIL>',
    type: 'rider',
    phone: '+91 9876543232',
    location: 'Indiranagar, Bangalore',
    reliabilityScore: 4.6,
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
  // Admin
  {
    id: 'admin_1',
    name: 'Admin Alice',
    email: '<EMAIL>',
    type: 'admin',
    company: 'FleetConnect',
    phone: '+91 9876543240',
    location: 'Bangalore',
    isActive: true,
    createdAt: new Date('2024-01-01'),
  },
]

// Mock Locations
export const mockLocations: Location[] = [
  { id: '1', name: 'Koramangala', pincode: '560034', city: 'Bangalore', state: 'Karnataka' },
  { id: '2', name: 'Indiranagar', pincode: '560038', city: 'Bangalore', state: 'Karnataka' },
  { id: '3', name: 'Marathahalli', pincode: '560037', city: 'Bangalore', state: 'Karnataka' },
  { id: '4', name: 'Whitefield', pincode: '560066', city: 'Bangalore', state: 'Karnataka' },
  { id: '5', name: 'Electronic City', pincode: '560100', city: 'Bangalore', state: 'Karnataka' },
  { id: '6', name: 'HSR Layout', pincode: '560102', city: 'Bangalore', state: 'Karnataka' },
  { id: '7', name: 'BTM Layout', pincode: '560029', city: 'Bangalore', state: 'Karnataka' },
  { id: '8', name: 'Jayanagar', pincode: '560011', city: 'Bangalore', state: 'Karnataka' },
]

// Mock Requirements
export const mockRequirements: Requirement[] = [
  {
    id: 'req_001',
    buyerId: 'buyer_swiggy',
    buyerCompany: 'Anonymous Buyer',
    title: 'Evening Delivery Rush - Koramangala',
    description: 'Need reliable riders for evening peak hours',
    quantity: 50,
    location: 'Koramangala',
    pincode: '560034',
    startDate: new Date('2024-12-20'),
    endDate: new Date('2024-12-20'),
    startTime: '19:00',
    endTime: '22:00',
    ratePerHour: 150,
    language: 'Kannada',
    status: 'bidding',
    createdAt: new Date('2024-12-18'),
    updatedAt: new Date('2024-12-18'),
    bids: [],
  },
  {
    id: 'req_002',
    buyerId: 'buyer_zomato',
    buyerCompany: 'Anonymous Buyer',
    title: 'Weekend Lunch Rush - Indiranagar',
    description: 'Weekend lunch delivery support needed',
    quantity: 30,
    location: 'Indiranagar',
    pincode: '560038',
    startDate: new Date('2024-12-21'),
    endDate: new Date('2024-12-22'),
    startTime: '12:00',
    endTime: '15:00',
    ratePerHour: 160,
    language: 'Hindi',
    status: 'pending',
    createdAt: new Date('2024-12-18'),
    updatedAt: new Date('2024-12-18'),
    bids: [],
  },
  {
    id: 'req_003',
    buyerId: 'buyer_blinkit',
    buyerCompany: 'Anonymous Buyer',
    title: 'Quick Commerce - Marathahalli',
    description: 'Urgent delivery support for quick commerce',
    quantity: 20,
    location: 'Marathahalli',
    pincode: '560037',
    startDate: new Date('2024-12-19'),
    endDate: new Date('2024-12-19'),
    startTime: '13:00',
    endTime: '17:00',
    ratePerHour: 170,
    language: 'English',
    status: 'confirmed',
    createdAt: new Date('2024-12-17'),
    updatedAt: new Date('2024-12-18'),
    bids: [],
  },
]

// Mock Bids
export const mockBids: Bid[] = [
  {
    id: 'bid_001',
    requirementId: 'req_001',
    supplierId: 'supplier_yana',
    supplierName: 'Yana Fleet Services',
    fulfillmentType: 'partial',
    quantity: 30,
    proposedRate: 150,
    message: 'We can provide 30 experienced riders for this slot',
    status: 'submitted',
    createdAt: new Date('2024-12-18'),
    updatedAt: new Date('2024-12-18'),
  },
  {
    id: 'bid_002',
    requirementId: 'req_001',
    supplierId: 'supplier_rapidriders',
    supplierName: 'Rapid Riders',
    fulfillmentType: 'partial',
    quantity: 15,
    proposedRate: 145,
    message: 'Available for partial fulfillment',
    status: 'submitted',
    createdAt: new Date('2024-12-18'),
    updatedAt: new Date('2024-12-18'),
  },
]

// Mock Rider Applications
export const mockRiderApplications: RiderApplication[] = [
  {
    id: 'app_001',
    riderId: 'rider_raj',
    riderName: 'Rider Raj',
    requirementId: 'req_001',
    location: 'Marathahalli',
    timeSlot: '19:00-22:00',
    language: 'Kannada',
    status: 'applied',
    createdAt: new Date('2024-12-18'),
    updatedAt: new Date('2024-12-18'),
  },
  {
    id: 'app_002',
    riderId: 'rider_kumar',
    riderName: 'Kumar K',
    requirementId: 'req_001',
    location: 'Koramangala',
    timeSlot: '19:00-22:00',
    language: 'Kannada',
    status: 'applied',
    createdAt: new Date('2024-12-18'),
    updatedAt: new Date('2024-12-18'),
  },
  {
    id: 'app_003',
    riderId: 'rider_priya',
    riderName: 'Priya P',
    requirementId: 'req_002',
    location: 'Indiranagar',
    timeSlot: '12:00-15:00',
    language: 'Hindi',
    status: 'confirmed',
    createdAt: new Date('2024-12-18'),
    updatedAt: new Date('2024-12-18'),
  },
]

export const languages = ['English', 'Hindi', 'Kannada', 'Tamil', 'Telugu']
export const timeSlots = [
  '06:00-09:00', '09:00-12:00', '12:00-15:00', '15:00-18:00', 
  '18:00-21:00', '21:00-24:00', '00:00-03:00', '03:00-06:00'
]
